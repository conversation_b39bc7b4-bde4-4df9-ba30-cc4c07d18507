import 'package:flutter/material.dart';
import '../../../constants/health_constants.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';
import '../services/health_suggestions_service.dart';

/// Unified health form widget that can handle different types of health records
class UnifiedHealthForm extends StatefulWidget {
  final String formType; // 'health', 'treatment', 'vaccination'
  final List<CattleIsar> cattle;
  final String? initialCattleId;
  final Map<String, dynamic>? initialData;
  final Function(Map<String, dynamic>) onSave;

  const UnifiedHealthForm({
    Key? key,
    required this.formType,
    required this.cattle,
    this.initialCattleId,
    this.initialData,
    required this.onSave,
  }) : super(key: key);

  @override
  State<UnifiedHealthForm> createState() => _UnifiedHealthFormState();
}

class _UnifiedHealthFormState extends State<UnifiedHealthForm> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Common fields
  String? _selectedCattleId;
  DateTime _selectedDate = DateTime.now();
  String _notes = '';
  double _cost = 0.0;
  String _veterinarian = '';

  // Health/Treatment specific fields
  String _condition = '';
  String _treatment = '';
  String _medicine = '';
  double _dose = 0.0;
  String _dosageUnit = 'mg';
  String _status = 'Active';

  // Vaccination specific fields
  String _vaccineName = '';
  String _manufacturer = '';
  String _batchNumber = '';
  DateTime? _nextDueDate;

  // UI state
  final bool _isLoading = false;
  bool _showAdvancedOptions = false;
  
  // Dropdown options
  List<String> _conditionOptions = [];
  List<String> _treatmentOptions = [];
  List<String> _medicineOptions = [];
  List<String> _vaccineOptions = [];
  
  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadDropdownOptions();
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  
  void _initializeForm() {
    // Set initial cattle selection
    if (widget.initialCattleId != null && widget.cattle.any((c) => c.tagId == widget.initialCattleId)) {
      _selectedCattleId = widget.initialCattleId;
    } else if (widget.cattle.isNotEmpty) {
      _selectedCattleId = widget.cattle.first.tagId;
    }

    // Load initial data if editing
    if (widget.initialData != null) {
      final data = widget.initialData!;

      // Validate cattle ID exists in the list
      final cattleId = data['cattleId'];
      if (cattleId != null && widget.cattle.any((c) => c.tagId == cattleId)) {
        _selectedCattleId = cattleId;
      }

      _selectedDate = data['date'] ?? DateTime.now();
      _notes = data['notes'] ?? '';
      _cost = data['cost']?.toDouble() ?? 0.0;
      _veterinarian = data['veterinarian'] ?? '';

      // Type-specific fields
      if (widget.formType == 'health') {
        _condition = data['condition'] ?? data['diagnosis'] ?? '';
        _medicine = data['medicine'] ?? '';
        _dose = data['dose']?.toDouble() ?? 0.0;
        _dosageUnit = data['dosageUnit'] ?? 'mg';
        _status = data['status'] ?? 'Healthy';
      } else if (widget.formType == 'treatment') {
        _condition = data['condition'] ?? data['diagnosis'] ?? '';
        _treatment = data['treatment'] ?? '';
        _medicine = data['medicine'] ?? '';
        _dose = data['dose']?.toDouble() ?? 0.0;
        _dosageUnit = data['dosageUnit'] ?? 'mg';
        _status = data['status'] ?? 'Active';
      } else if (widget.formType == 'vaccination') {
        _vaccineName = data['vaccineName'] ?? '';
        _manufacturer = data['manufacturer'] ?? '';
        _batchNumber = data['batchNumber'] ?? '';
        _nextDueDate = data['nextDueDate'];
      }
    }
  }
  
  void _loadDropdownOptions() async {
    // Get animal type for selected cattle
    final selectedCattle = widget.cattle.firstWhere(
      (c) => c.tagId == _selectedCattleId,
      orElse: () => widget.cattle.first,
    );

    // Use category or a default animal type since animalType doesn't exist
    final animalType = selectedCattle.category?.toLowerCase() ?? 'general';

    // Load saved vaccine names from database
    List<String> savedVaccines = [];
    if (widget.formType == 'vaccination') {
      try {
        savedVaccines = await HealthSuggestionsService.getFrequentVaccines(animalType: animalType);
      } catch (e) {
        // If there's an error loading saved vaccines, continue with default options
        print('Error loading saved vaccines: $e');
      }
    }

    setState(() {
      _conditionOptions = HealthConstants.getConditionsForAnimalType(animalType);
      _treatmentOptions = HealthConstants.commonTreatments;
      _medicineOptions = HealthConstants.getAllMedicines();

      // Combine default vaccines with saved vaccines, removing duplicates
      final defaultVaccines = HealthConstants.getVaccinesForAnimalType(animalType);
      final allVaccines = <String>{...savedVaccines, ...defaultVaccines}.toList();
      allVaccines.sort(); // Sort alphabetically
      _vaccineOptions = allVaccines;
    });
  }
  
  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && mounted) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }
  
  Future<void> _selectNextDueDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _nextDueDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
    );
    
    if (picked != null && mounted) {
      setState(() {
        _nextDueDate = picked;
      });
    }
  }
  
  void _onCattleChanged(String? cattleId) {
    if (cattleId != null && cattleId != _selectedCattleId) {
      setState(() {
        _selectedCattleId = cattleId;
        // Reset form fields when cattle changes to avoid validation issues
        _condition = '';
        _treatment = '';
        _medicine = '';
        _vaccineName = '';
      });
      _loadDropdownOptions(); // Reload options for new animal type
    }
  }
  
  bool _validateForm() {
    if (!_formKey.currentState!.validate()) {
      return false;
    }
    
    // Additional validation based on form type
    if (widget.formType == 'health') {
      if (_condition.isEmpty) {
        MessageUtils.showError(context, 'Please select or enter a condition');
        return false;
      }
    } else if (widget.formType == 'treatment') {
      if (_condition.isEmpty) {
        MessageUtils.showError(context, 'Please select or enter a condition');
        return false;
      }
      if (_treatment.isEmpty) {
        MessageUtils.showError(context, 'Please select or enter a treatment');
        return false;
      }
    } else if (widget.formType == 'vaccination') {
      if (_vaccineName.isEmpty) {
        MessageUtils.showError(context, 'Please select or enter a vaccine name');
        return false;
      }
    }
    
    return true;
  }
  
  void _saveForm() {
    if (!_validateForm()) return;
    
    final formData = <String, dynamic>{
      'cattleId': _selectedCattleId,
      'date': _selectedDate,
      'notes': _notes,
      'cost': _cost,
      'veterinarian': _veterinarian,
    };
    
    // Add type-specific fields
    if (widget.formType == 'health') {
      formData.addAll({
        'condition': _condition,
        'diagnosis': _condition, // For backward compatibility
        'medicine': _medicine,
        'dose': _dose,
        'dosageUnit': _dosageUnit,
        'status': _status,
      });
    } else if (widget.formType == 'treatment') {
      formData.addAll({
        'condition': _condition,
        'diagnosis': _condition, // For backward compatibility
        'treatment': _treatment,
        'medicine': _medicine,
        'dose': _dose,
        'dosageUnit': _dosageUnit,
        'status': _status,
      });
    } else if (widget.formType == 'vaccination') {
      formData.addAll({
        'vaccineName': _vaccineName,
        'manufacturer': _manufacturer,
        'batchNumber': _batchNumber,
        'nextDueDate': _nextDueDate,
        'status': 'Completed',
      });
    }
    
    widget.onSave(formData);
  }
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Form(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minWidth: constraints.maxWidth,
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildCattleDropdown(),
                    const SizedBox(height: 16),
                    _buildDateField(),
                    const SizedBox(height: 16),

                    // Type-specific fields
                    if (widget.formType == 'health') ...[
                      _buildConditionField(),
                      const SizedBox(height: 16),
                      _buildHealthStatusField(),
                      const SizedBox(height: 16),
                      _buildAdvancedToggle(),
                      if (_showAdvancedOptions) ...[
                        const SizedBox(height: 16),
                        _buildMedicineField(),
                        const SizedBox(height: 16),
                        _buildDosageFields(),
                      ],
                    ] else if (widget.formType == 'treatment') ...[
                      _buildConditionField(),
                      const SizedBox(height: 16),
                      _buildTreatmentField(),
                      const SizedBox(height: 16),
                      _buildAdvancedToggle(),
                      if (_showAdvancedOptions) ...[
                        const SizedBox(height: 16),
                        _buildMedicineField(),
                        const SizedBox(height: 16),
                        _buildDosageFields(),
                        const SizedBox(height: 16),
                        _buildTreatmentStatusField(),
                      ],
                    ] else if (widget.formType == 'vaccination') ...[
                      _buildVaccineNameField(),
                      const SizedBox(height: 16),
                      _buildAdvancedToggle(),
                      if (_showAdvancedOptions) ...[
                        const SizedBox(height: 16),
                        _buildManufacturerField(),
                        const SizedBox(height: 16),
                        _buildBatchNumberField(),
                        const SizedBox(height: 16),
                        _buildNextDueDateField(),
                      ],
                    ],

                    const SizedBox(height: 16),
                    _buildVeterinarianField(),
                    const SizedBox(height: 16),
                    _buildCostField(),
                    const SizedBox(height: 16),
                    _buildNotesField(),
                    const SizedBox(height: 24),
                    _buildSaveButton(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildCattleDropdown() {
    // Remove duplicates and ensure unique cattle entries by tagId
    final uniqueCattleMap = <String, CattleIsar>{};
    for (final cattle in widget.cattle) {
      if (cattle.tagId != null && cattle.tagId!.isNotEmpty) {
        uniqueCattleMap[cattle.tagId!] = cattle;
      }
    }
    final uniqueCattle = uniqueCattleMap.values.toList();

    // Ensure the selected value exists in the items list
    final validCattleIds = uniqueCattle.map((c) => c.tagId).where((id) => id != null).toSet();
    final selectedValue = validCattleIds.contains(_selectedCattleId) ? _selectedCattleId : null;

    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: 'Select Cattle',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.pets, color: Colors.brown),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: uniqueCattle.map((cattle) {
        return DropdownMenuItem<String>(
          value: cattle.tagId,
          child: Text(
            '${cattle.name ?? 'Unnamed'} (${cattle.tagId})',
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: _onCattleChanged,
      validator: (value) => value == null ? 'Please select cattle' : null,
    );
  }
  
  Widget _buildDateField() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'Date',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.calendar_today, color: Colors.blue),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        suffixIcon: IconButton(
          icon: const Icon(Icons.edit),
          onPressed: _selectDate,
        ),
      ),
      controller: TextEditingController(
        text: '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
      ),
    );
  }
  
  Widget _buildConditionField() {
    // Ensure the selected value exists in the items list
    final allConditionOptions = [..._conditionOptions, 'custom'];
    final selectedValue = _condition.isNotEmpty && allConditionOptions.contains(_condition) ? _condition : null;

    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: 'Condition/Diagnosis',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: Icon(Icons.medical_services, color: HealthConstants.getConditionColor(_condition)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: [
        ..._conditionOptions.map((condition) => DropdownMenuItem<String>(
          value: condition,
          child: Text(
            condition,
            overflow: TextOverflow.ellipsis,
          ),
        )),
        const DropdownMenuItem<String>(
          value: 'custom',
          child: Text('Other (Custom)'),
        ),
      ],
      onChanged: (value) {
        if (value == 'custom') {
          _showCustomConditionDialog();
        } else if (value != null) {
          setState(() {
            _condition = value;
          });
        }
      },
      validator: (value) => _condition.isEmpty ? 'Please select or enter a condition' : null,
    );
  }

  Widget _buildTreatmentField() {
    // Ensure the selected value exists in the items list
    final allTreatmentOptions = [..._treatmentOptions, 'custom'];
    final selectedValue = _treatment.isNotEmpty && allTreatmentOptions.contains(_treatment) ? _treatment : null;

    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: 'Treatment',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.healing, color: Colors.green),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: [
        ..._treatmentOptions.map((treatment) => DropdownMenuItem<String>(
          value: treatment,
          child: Text(
            treatment,
            overflow: TextOverflow.ellipsis,
          ),
        )),
        const DropdownMenuItem<String>(
          value: 'custom',
          child: Text('Other (Custom)'),
        ),
      ],
      onChanged: (value) {
        if (value == 'custom') {
          _showCustomTreatmentDialog();
        } else if (value != null) {
          setState(() {
            _treatment = value;
          });
        }
      },
      validator: (value) => _treatment.isEmpty ? 'Please select or enter a treatment' : null,
    );
  }

  Widget _buildAdvancedToggle() {
    return Row(
      children: [
        Icon(
          _showAdvancedOptions ? Icons.expand_less : Icons.expand_more,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        TextButton(
          onPressed: () {
            setState(() {
              _showAdvancedOptions = !_showAdvancedOptions;
            });
          },
          child: Text(
            _showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ),
      ],
    );
  }

  Widget _buildMedicineField() {
    // Ensure the selected value exists in the items list
    final allMedicineOptions = ['', ..._medicineOptions, 'custom'];
    final selectedValue = allMedicineOptions.contains(_medicine) ? _medicine : '';

    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: 'Medicine (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.medication, color: Colors.purple),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: [
        const DropdownMenuItem<String>(value: '', child: Text('None')),
        ..._medicineOptions.map((medicine) => DropdownMenuItem<String>(
          value: medicine,
          child: Text(
            medicine,
            overflow: TextOverflow.ellipsis,
          ),
        )),
        const DropdownMenuItem<String>(
          value: 'custom',
          child: Text('Other (Custom)'),
        ),
      ],
      onChanged: (value) {
        if (value == 'custom') {
          _showCustomMedicineDialog();
        } else {
          setState(() {
            _medicine = value ?? '';
          });
        }
      },
    );
  }

  Widget _buildDosageFields() {
    return Column(
      children: [
        // Dose field
        TextFormField(
          decoration: InputDecoration(
            labelText: 'Dose',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: const Icon(Icons.straighten, color: Colors.cyan),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.number,
          initialValue: _dose > 0 ? _dose.toString() : '',
          onChanged: (value) {
            _dose = double.tryParse(value) ?? 0.0;
          },
        ),
        const SizedBox(height: 16),
        // Unit field
        DropdownButtonFormField<String>(
          value: _dosageUnit,
          decoration: InputDecoration(
            labelText: 'Dosage Unit',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: const Icon(Icons.scale, color: Colors.cyan),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          isExpanded: true, // This prevents overflow
          items: HealthConstants.dosageUnits.map((unit) => DropdownMenuItem<String>(
            value: unit,
            child: Text(
              unit,
              overflow: TextOverflow.ellipsis,
            ),
          )).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _dosageUnit = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildHealthStatusField() {
    return DropdownButtonFormField<String>(
      value: _status,
      decoration: InputDecoration(
        labelText: 'Health Status',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.health_and_safety, color: Colors.green),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: HealthConstants.healthStatus.map((status) => DropdownMenuItem<String>(
        value: status,
        child: Text(
          status,
          overflow: TextOverflow.ellipsis,
        ),
      )).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _status = value;
          });
        }
      },
    );
  }

  Widget _buildTreatmentStatusField() {
    return DropdownButtonFormField<String>(
      value: _status,
      decoration: InputDecoration(
        labelText: 'Treatment Status',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.flag, color: Colors.indigo),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: HealthConstants.treatmentStatus.map((status) => DropdownMenuItem<String>(
        value: status,
        child: Text(
          status,
          overflow: TextOverflow.ellipsis,
        ),
      )).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _status = value;
          });
        }
      },
    );
  }

  Widget _buildVaccineNameField() {
    // Ensure the selected value exists in the items list
    final allVaccineOptions = [..._vaccineOptions, 'custom'];
    final selectedValue = _vaccineName.isNotEmpty && allVaccineOptions.contains(_vaccineName) ? _vaccineName : null;

    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: 'Vaccine Name',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.vaccines, color: Colors.teal),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      isExpanded: true, // This prevents overflow
      items: [
        ..._vaccineOptions.map((vaccine) => DropdownMenuItem<String>(
          value: vaccine,
          child: Text(
            vaccine,
            overflow: TextOverflow.ellipsis,
          ),
        )),
        const DropdownMenuItem<String>(
          value: 'custom',
          child: Text('Other (Custom)'),
        ),
      ],
      onChanged: (value) {
        if (value == 'custom') {
          _showCustomVaccineDialog();
        } else if (value != null) {
          setState(() {
            _vaccineName = value;
          });
        }
      },
      validator: (value) => _vaccineName.isEmpty ? 'Please select or enter a vaccine name' : null,
    );
  }

  Widget _buildManufacturerField() {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Manufacturer (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.business, color: Colors.blue),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      initialValue: _manufacturer,
      onChanged: (value) => _manufacturer = value,
    );
  }

  Widget _buildBatchNumberField() {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Batch Number (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.qr_code, color: Colors.grey),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      initialValue: _batchNumber,
      onChanged: (value) => _batchNumber = value,
    );
  }

  Widget _buildNextDueDateField() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'Next Due Date (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.schedule, color: Colors.cyan),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _selectNextDueDate,
            ),
            if (_nextDueDate != null)
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () => setState(() => _nextDueDate = null),
              ),
          ],
        ),
      ),
      controller: TextEditingController(
        text: _nextDueDate != null
          ? '${_nextDueDate!.day}/${_nextDueDate!.month}/${_nextDueDate!.year}'
          : '',
      ),
    );
  }

  Widget _buildVeterinarianField() {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Veterinarian (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.person, color: Colors.indigo),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      initialValue: _veterinarian,
      onChanged: (value) => _veterinarian = value,
    );
  }

  Widget _buildCostField() {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Cost (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.attach_money, color: Colors.green),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      keyboardType: TextInputType.number,
      initialValue: _cost > 0 ? _cost.toString() : '',
      onChanged: (value) => _cost = double.tryParse(value) ?? 0.0,
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Notes (Optional)',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        prefixIcon: const Icon(Icons.notes, color: Colors.grey),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      ),
      maxLines: 3,
      initialValue: _notes,
      onChanged: (value) => _notes = value,
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveForm,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: _isLoading
          ? const CircularProgressIndicator()
          : Text(
              widget.initialData == null ? 'Save Record' : 'Update Record',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
      ),
    );
  }

  // Custom input dialogs
  void _showCustomConditionDialog() {
    _showCustomInputDialog(
      title: 'Enter Custom Condition',
      hint: 'Enter condition/diagnosis',
      onSave: (value) => setState(() => _condition = value),
    );
  }

  void _showCustomTreatmentDialog() {
    _showCustomInputDialog(
      title: 'Enter Custom Treatment',
      hint: 'Enter treatment',
      onSave: (value) => setState(() => _treatment = value),
    );
  }

  void _showCustomMedicineDialog() {
    _showCustomInputDialog(
      title: 'Enter Custom Medicine',
      hint: 'Enter medicine name',
      onSave: (value) => setState(() => _medicine = value),
    );
  }

  void _showCustomVaccineDialog() {
    _showCustomInputDialog(
      title: 'Enter Custom Vaccine',
      hint: 'Enter vaccine name',
      onSave: (value) => setState(() => _vaccineName = value),
    );
  }

  void _showCustomInputDialog({
    required String title,
    required String hint,
    required Function(String) onSave,
  }) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                onSave(controller.text.trim());
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
